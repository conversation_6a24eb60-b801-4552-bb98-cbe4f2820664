<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gofile

```go
import "github.com/real-rm/gofile"
```

## Index

- [Constants](<#constants>)
- [func DownloadAndResizeImage\(url string, width, height int\) \(image.Image, error\)](<#DownloadAndResizeImage>)
- [func DownloadAndSaveFile\(opts \*DownloadAndSaveFileOptions\) \(\[\]string, error\)](<#DownloadAndSaveFile>)
- [func DownloadAndSaveImageInDirs\(url string, savePaths \[\]string, compressWebP bool, maxRetries ...int\) \(map\[string\]string, error\)](<#DownloadAndSaveImageInDirs>)
- [func DownloadAndSaveImageInDirsWithCompression\(url string, savePaths \[\]string, compressWebP bool, compressToSize bool, targetSizeKB int, maxRetries ...int\) \(map\[string\]string, error\)](<#DownloadAndSaveImageInDirsWithCompression>)
- [func DownloadAndSaveImageWithSmartCompression\(url string, savePath string, targetSizeKB int, compressWebP bool, maxRetries ...int\) \(string, error\)](<#DownloadAndSaveImageWithSmartCompression>)
- [func SaveImage\(img image.Image, savePath string, compressWebP bool\) \(string, error\)](<#SaveImage>)
- [func SetTestHTTPClient\(client \*http.Client\)](<#SetTestHTTPClient>)
- [type DownloadAndSaveFileOptions](<#DownloadAndSaveFileOptions>)


## Constants

<a name="DEFAULT_RETRIES"></a>

```go
const (
    DEFAULT_RETRIES = 3
    // Memory limits for image processing
    MAX_IMAGE_MEMORY_MB  = 100              // Restored to 100MB but with better memory management
    MAX_IMAGE_SIZE_BYTES = 10 * 1024 * 1024 // 10MB limit with waiting instead of skipping

    // Image compression constants
    DEFAULT_TARGET_SIZE_KB = 300  // Default target size in KB for image compression
    MAX_DIMENSION_PIXELS   = 1280 // Maximum dimension (width or height) for compressed images
)
```

<a name="DownloadAndResizeImage"></a>
## func [DownloadAndResizeImage](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L674>)

```go
func DownloadAndResizeImage(url string, width, height int) (image.Image, error)
```

DownloadAndResizeImage downloads an image from URL and returns the resized image width and height are the target dimensions, the image will maintain its aspect ratio and fit within these dimensions

<a name="DownloadAndSaveFile"></a>
## func [DownloadAndSaveFile](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L1091>)

```go
func DownloadAndSaveFile(opts *DownloadAndSaveFileOptions) ([]string, error)
```

DownloadAndSaveFile downloads a file and saves it to the specified format Returns the path to the saved file and any error that occurred

<a name="DownloadAndSaveImageInDirs"></a>
## func [DownloadAndSaveImageInDirs](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L576>)

```go
func DownloadAndSaveImageInDirs(url string, savePaths []string, compressWebP bool, maxRetries ...int) (map[string]string, error)
```

DownloadAndSaveImageInDirs downloads an image from URL and saves it to specified paths savePaths is a list of full paths including filename and extension If compressWebP is true, attempts to convert the image to WebP format with compression Falls back to JPEG if WebP encoding is not available When compressWebP=false, converts to JPEG only if target extension is .jpg/.jpeg, otherwise saves original data Returns a map \[savePath\]savedPath and any error that occurred

<a name="DownloadAndSaveImageInDirsWithCompression"></a>
## func [DownloadAndSaveImageInDirsWithCompression](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L585>)

```go
func DownloadAndSaveImageInDirsWithCompression(url string, savePaths []string, compressWebP bool, compressToSize bool, targetSizeKB int, maxRetries ...int) (map[string]string, error)
```

DownloadAndSaveImageInDirsWithCompression downloads an image from URL and saves it to specified paths with optional smart compression savePaths is a list of full paths including filename and extension If compressWebP is true, attempts to convert the image to WebP format with compression If compressToSize is true, intelligently compresses the image to targetSizeKB Returns a map \[savePath\]savedPath and any error that occurred

<a name="DownloadAndSaveImageWithSmartCompression"></a>
## func [DownloadAndSaveImageWithSmartCompression](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L1189>)

```go
func DownloadAndSaveImageWithSmartCompression(url string, savePath string, targetSizeKB int, compressWebP bool, maxRetries ...int) (string, error)
```

DownloadAndSaveImageWithSmartCompression downloads image and intelligently compresses to specified size This is a convenience function for quick implementation of image compression functionality

<a name="SaveImage"></a>
## func [SaveImage](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L502>)

```go
func SaveImage(img image.Image, savePath string, compressWebP bool) (string, error)
```

SaveImage saves the provided image data to the specified path If compressWebP is true, attempts to convert the image to WebP format with compression Falls back to JPEG if WebP encoding is not available Returns the final saved file path and any error that occurred

<a name="SetTestHTTPClient"></a>
## func [SetTestHTTPClient](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L121>)

```go
func SetTestHTTPClient(client *http.Client)
```

SetTestHTTPClient sets a custom HTTP client for testing purposes Call with nil to reset to default behavior

<a name="DownloadAndSaveFileOptions"></a>
## type [DownloadAndSaveFileOptions](<https://github.com/real-rm/gofile/blob/main/fetch_and_save.go#L1079-L1087>)



```go
type DownloadAndSaveFileOptions struct {
    URL            string   // URL to download from
    SavePaths      []string // Paths to save the file to
    CompressWebP   bool     // Whether to compress as WebP (only for images)
    IsPhoto        bool     // Whether the file is a photo
    MaxRetries     int      // Maximum number of retry attempts
    CompressToSize bool     // Whether to enable smart compression to target size
    TargetSizeKB   int      // Target file size in KB (default: 300)
}
```

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
