package gofile

import (
	"image"
	"image/color"
	"image/jpeg"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// createLargeTestImage 创建一个大尺寸的测试图片
func createLargeTestImage(width, height int) image.Image {
	img := image.NewRGBA(image.Rect(0, 0, width, height))

	// 创建渐变效果，增加文件大小
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			r := uint8((x * 255) / width)
			g := uint8((y * 255) / height)
			b := uint8(((x + y) * 255) / (width + height))
			img.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	return img
}

func TestSmartCompression(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name          string
		imageWidth    int
		imageHeight   int
		targetSizeKB  int
		compressWebP  bool
		expectSuccess bool
	}{
		{
			name:          "Large image to 300KB JPEG",
			imageWidth:    1200,
			imageHeight:   800,
			targetSizeKB:  300,
			compressWebP:  false,
			expectSuccess: true,
		},
		{
			name:          "Large image to 100KB JPEG",
			imageWidth:    1000,
			imageHeight:   1000,
			targetSizeKB:  100,
			compressWebP:  false,
			expectSuccess: true,
		},
		{
			name:          "Large image to 300KB WebP",
			imageWidth:    1200,
			imageHeight:   800,
			targetSizeKB:  300,
			compressWebP:  true,
			expectSuccess: true,
		},
		{
			name:          "Small image within target size",
			imageWidth:    800,
			imageHeight:   600,
			targetSizeKB:  100, // Changed from 10 to 100 to match new logic
			compressWebP:  false,
			expectSuccess: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试服务器
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)

				// 创建大图片
				img := createLargeTestImage(tt.imageWidth, tt.imageHeight)
				err := jpeg.Encode(w, img, &jpeg.Options{Quality: 95})
				require.NoError(t, err)
			}))
			defer server.Close()

			// 创建临时目录
			tmpDir := createTempDir(t)
			var savePath string
			if tt.compressWebP {
				savePath = filepath.Join(tmpDir, "compressed.webp")
			} else {
				savePath = filepath.Join(tmpDir, "compressed.jpg")
			}

			// 测试智能压缩
			savedPath, err := DownloadAndSaveImageWithSmartCompression(
				server.URL,
				savePath,
				tt.targetSizeKB,
				tt.compressWebP,
				1, // maxRetries
			)

			if tt.expectSuccess {
				assert.NoError(t, err)
				assert.NotEmpty(t, savedPath)
				assert.FileExists(t, savedPath)

				// 检查文件大小
				fileInfo, err := os.Stat(savedPath)
				require.NoError(t, err)

				fileSizeKB := fileInfo.Size() / 1024
				t.Logf("Target: %dKB, Actual: %dKB", tt.targetSizeKB, fileSizeKB)

				// New logic: if image is within target size, return as-is
				// If image is larger than target, resize to max dimension (1280px)
				if tt.targetSizeKB >= 100 { // For reasonable target sizes
					// Allow some flexibility for compression results
					maxAllowedSizeKB := int64(tt.targetSizeKB) * 150 / 100
					assert.LessOrEqual(t, fileSizeKB, maxAllowedSizeKB,
						"File size %dKB should be close to target %dKB", fileSizeKB, tt.targetSizeKB)
				} else {
					// For very small targets, just ensure the file exists and is reasonable
					assert.Greater(t, fileSizeKB, int64(0), "File should have some content")
				}
			} else {
				assert.Error(t, err)
			}
		})
	}
}

func TestDownloadAndSaveFileWithSmartCompression(t *testing.T) {
	setupTestProcess(t)

	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "image/jpeg")
		w.WriteHeader(http.StatusOK)

		// 创建大图片
		img := createLargeTestImage(1000, 800)
		err := jpeg.Encode(w, img, &jpeg.Options{Quality: 95})
		require.NoError(t, err)
	}))
	defer server.Close()

	tmpDir := createTempDir(t)
	savePath := filepath.Join(tmpDir, "smart_compressed.jpg")

	// 测试通过 DownloadAndSaveFile 使用智能压缩
	opts := &DownloadAndSaveFileOptions{
		URL:            server.URL,
		SavePaths:      []string{savePath},
		IsPhoto:        true,
		CompressWebP:   false,
		CompressToSize: true,
		TargetSizeKB:   200,
		MaxRetries:     1,
	}

	results, err := DownloadAndSaveFile(opts)
	assert.NoError(t, err)
	assert.Len(t, results, 1)
	assert.FileExists(t, results[0])

	// 检查文件大小
	fileInfo, err := os.Stat(results[0])
	require.NoError(t, err)

	fileSizeKB := fileInfo.Size() / 1024
	t.Logf("Target: 200KB, Actual: %dKB", fileSizeKB)

	// 文件大小应该接近目标大小
	assert.LessOrEqual(t, fileSizeKB, int64(300), // 允许150%的误差
		"File size %dKB should be close to target 200KB", fileSizeKB)
}

func TestCompressImageToTargetSize(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name         string
		imageWidth   int
		imageHeight  int
		targetSizeKB int
		isWebP       bool
	}{
		{
			name:         "JPEG compression",
			imageWidth:   800,
			imageHeight:  600,
			targetSizeKB: 150,
			isWebP:       false,
		},
		{
			name:         "WebP compression",
			imageWidth:   800,
			imageHeight:  600,
			targetSizeKB: 150,
			isWebP:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试图片
			img := createLargeTestImage(tt.imageWidth, tt.imageHeight)

			// 测试压缩函数
			compressedImg, finalSizeKB, err := compressImageToTargetSize(img, tt.targetSizeKB, tt.isWebP, "test.jpg")

			assert.NoError(t, err)
			assert.NotNil(t, compressedImg)
			assert.Greater(t, finalSizeKB, 0)

			t.Logf("Original: %dx%d, Target: %dKB, Final: %dKB",
				tt.imageWidth, tt.imageHeight, tt.targetSizeKB, finalSizeKB)

			// 检查压缩后的尺寸
			bounds := compressedImg.Bounds()
			compressedWidth := bounds.Dx()
			compressedHeight := bounds.Dy()

			t.Logf("Compressed dimensions: %dx%d", compressedWidth, compressedHeight)

			// 如果目标大小较小，压缩后的尺寸应该小于原始尺寸
			if tt.targetSizeKB < 200 {
				assert.True(t, compressedWidth <= tt.imageWidth || compressedHeight <= tt.imageHeight,
					"Compressed image should be smaller than original")
			}
		})
	}
}
