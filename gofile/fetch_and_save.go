package gofile

import (
	"bytes"
	"fmt"
	"io"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"image"
	"image/draw"
	"image/jpeg"

	nativewebp "github.com/HugoSmits86/nativewebp"
	golog "github.com/real-rm/golog"
	xdraw "golang.org/x/image/draw"
)

const (
	DEFAULT_RETRIES = 3
	// Memory limits for image processing
	MAX_IMAGE_MEMORY_MB  = 100              // Restored to 100MB but with better memory management
	MAX_IMAGE_SIZE_BYTES = 10 * 1024 * 1024 // 10MB limit with waiting instead of skipping

	// Default values for configurable memory limits
	defaultMaxMemoryForProcessing = 800 * 1024 * 1024 // 800MB
	defaultMaxGoroutines          = 100

	// Image compression constants
	DEFAULT_TARGET_SIZE_KB = 300  // Default target size in KB for image compression
	MAX_DIMENSION_PIXELS   = 1280 // Maximum dimension (width or height) for compressed images
)

// getEnvAsInt64 gets an environment variable as int64 with a default value
func getEnvAsInt64(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseInt(value, 10, 64); err == nil {
			return parsed
		}
	}
	return defaultValue
}

// getEnvAsInt gets an environment variable as int with a default value
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.Atoi(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

// Global HTTP client to prevent connection leaks
var (
	globalHTTPClient *http.Client
	clientOnce       sync.Once
	testHTTPClient   *http.Client // For testing purposes only

	// Configurable memory limits
	maxMemoryForProcessing = getEnvAsInt64("GOFILE_MAX_MEMORY_FOR_PROCESSING", defaultMaxMemoryForProcessing)
	maxGoroutines          = getEnvAsInt("GOFILE_MAX_GOROUTINES", defaultMaxGoroutines)

	// Smart GC control variables
	processedImageCount int64
	lastGCTime          time.Time
	gcBatchSize         = getEnvAsInt64("GOFILE_GC_BATCH_SIZE", 1000)                                     // 1000 images
	gcMinInterval       = time.Duration(getEnvAsInt64("GOFILE_GC_MIN_INTERVAL_MINUTES", 5)) * time.Minute // 5 minutes
	gcEmergencyMemoryGB = getEnvAsInt64("GOFILE_GC_EMERGENCY_MEMORY_GB", 2)                               // 2GB

	// HTTP timeout configuration - can be overridden via environment variables
	httpTimeoutSeconds        = getEnvAsInt64("GOFILE_HTTP_TIMEOUT_SECONDS", 120)       // total timeout
	httpResponseHeaderTimeout = getEnvAsInt64("GOFILE_HTTP_HEADER_TIMEOUT_SECONDS", 30) // response header timeout
	httpDialTimeout           = getEnvAsInt64("GOFILE_HTTP_DIAL_TIMEOUT_SECONDS", 15)   // dial timeout
	httpTLSTimeout            = getEnvAsInt64("GOFILE_HTTP_TLS_TIMEOUT_SECONDS", 15)    // TLS handshake timeout
)

// getHTTPClient returns a singleton HTTP client with proper connection management
func getHTTPClient() *http.Client {
	// For testing: allow override with test client
	if testHTTPClient != nil {
		return testHTTPClient
	}

	clientOnce.Do(func() {
		globalHTTPClient = &http.Client{
			Timeout: time.Duration(httpTimeoutSeconds) * time.Second, // Configurable total timeout
			Transport: &http.Transport{
				ResponseHeaderTimeout: time.Duration(httpResponseHeaderTimeout) * time.Second, // Configurable header timeout
				ExpectContinueTimeout: 10 * time.Second,                                       // Keep at 10s
				IdleConnTimeout:       5 * time.Second,                                        // Keep at 5s
				TLSHandshakeTimeout:   time.Duration(httpTLSTimeout) * time.Second,            // Configurable TLS timeout
				DialContext: (&net.Dialer{
					Timeout:   time.Duration(httpDialTimeout) * time.Second, // Configurable dial timeout
					KeepAlive: 0,                                            // Disable keep-alive completely
				}).DialContext,
				DisableKeepAlives:      true,  // Disable keep-alives
				MaxIdleConns:           0,     // No idle connections
				MaxConnsPerHost:        3,     // Keep at 3 for better performance
				MaxIdleConnsPerHost:    0,     // No idle connections per host
				DisableCompression:     true,  // Keep compression disabled
				ForceAttemptHTTP2:      false, // Keep HTTP/2 disabled
				MaxResponseHeaderBytes: 16384, // Keep at 16KB for larger headers
			},
		}
		golog.Info("Global HTTP client initialized with extended timeouts for better reliability")
	})
	return globalHTTPClient
}

// SetTestHTTPClient sets a custom HTTP client for testing purposes
// Call with nil to reset to default behavior
func SetTestHTTPClient(client *http.Client) {
	testHTTPClient = client
}

// isTimeoutError checks if an error is a timeout error and returns appropriate logging info
func isTimeoutError(err error) (bool, string) {
	errorType := fmt.Sprintf("%T", err)

	// Check if it's a timeout error
	if ne, ok := err.(net.Error); ok && ne.Timeout() {
		return true, errorType
	} else if os.IsTimeout(err) {
		return true, errorType
	} else {
		// Check for context deadline exceeded specifically
		errStr := err.Error()
		if strings.Contains(errStr, "context deadline exceeded") || strings.Contains(errStr, "Client.Timeout") {
			return true, errorType
		}
	}
	return false, errorType
}

// downloadWithRetry attempts to download from a URL with retries
// Returns the response and error if any
// If maxRetries is not provided, uses DEFAULT_RETRIES (1)
func downloadWithRetry(url string, maxRetries ...int) (*http.Response, error) {
	retries := DEFAULT_RETRIES
	if len(maxRetries) > 0 {
		retries = maxRetries[0]
	}

	// Use global HTTP client to prevent connection leaks
	client := getHTTPClient()

	// Always use our global client to prevent connection leaks
	// Never use http.DefaultClient
	for i := 0; i < retries; i++ {
		// Only log download attempts for retries (not the first attempt)
		if i > 0 {
			golog.Info("Download retry attempt", "url", url, "attempt", i+1, "maxRetries", retries)
		}
		resp, err := client.Get(url)
		if err != nil {
			// Force cleanup of any leaked connections after error
			if transport, ok := client.Transport.(*http.Transport); ok {
				transport.CloseIdleConnections()
			}

			// Enhanced error type detection and logging
			isTimeout, errorType := isTimeoutError(err)
			if isTimeout {
				golog.Warn("Timeout detected during download", "url", url, "attempt", i+1, "err", err, "errorType", errorType)
			}

			if isTimeout {
				if i < retries-1 {
					// Exponential backoff with longer waits for timeouts
					waitTime := time.Duration((i+1)*3) * time.Second // Increased wait time
					golog.Info("Retrying after timeout", "waitTime", waitTime, "attempt", i+1, "maxRetries", retries)
					time.Sleep(waitTime)
					continue
				}
				return nil, fmt.Errorf("request timed out after %d attempts: %w", retries, err)
			}

			if i < retries-1 {
				golog.Warn("Non-timeout error, retrying", "url", url, "attempt", i+1, "error", err, "errorType", errorType)
				// Longer wait between retries for better success rate
				waitTime := time.Duration((i+1)*2) * time.Second
				time.Sleep(waitTime)
				continue
			}
			return nil, fmt.Errorf("failed download after %d attempts: %w", retries, err)
		}

		// At this point, we have a response
		if resp.StatusCode == http.StatusOK {
			return resp, nil
		}

		// For non-200 responses, drain and close the body to prevent connection leaks
		if resp.Body != nil {
			// Drain the body completely before closing
			if _, err := io.Copy(io.Discard, resp.Body); err != nil {
				golog.Error("failed to drain response body", "error", err)
			}
			if closeErr := resp.Body.Close(); closeErr != nil {
				golog.Error("failed to close response body", "error", closeErr)
			}
		}

		// Force cleanup of connections after non-200 response
		if transport, ok := client.Transport.(*http.Transport); ok {
			transport.CloseIdleConnections()
		}

		if i < retries-1 {
			golog.Warn("retry downloading",
				"url", url,
				"attempt", i+1,
				"status", resp.StatusCode)
			// Longer wait for non-200 responses
			waitTime := time.Duration((i+1)*2) * time.Second
			time.Sleep(waitTime)
		}
	}

	return nil, fmt.Errorf("failed after %d attempts", retries)
}

// readBodyWithRetry reads response body with retry logic for timeout/network errors
func readBodyWithRetry(resp *http.Response, url string, maxRetries int) ([]byte, error) {
	for i := 0; i < maxRetries; i++ {
		// Only log body reading attempts for retries (not the first attempt)
		if i > 0 {
			golog.Info("Body reading retry attempt", "url", url, "attempt", i+1, "maxRetries", maxRetries)
		}

		// Try to read the response body
		data, err := io.ReadAll(resp.Body)
		if err != nil {
			// Enhanced error type detection for body reading errors
			isTimeout, errorType := isTimeoutError(err)
			if isTimeout {
				golog.Warn("Timeout detected during body reading", "url", url, "attempt", i+1, "err", err, "errorType", errorType)
			}

			if isTimeout {
				if i < maxRetries-1 {
					// Longer exponential backoff for body reading timeouts (large images need more time)
					waitTime := time.Duration((i+1)*10) * time.Second // Increased from 3s to 5s
					golog.Info("Retrying after body reading timeout", "waitTime", waitTime, "attempt", i+1, "maxRetries", maxRetries)
					time.Sleep(waitTime)
					continue
				}
				return nil, fmt.Errorf("body reading timed out after %d attempts: %w", maxRetries, err)
			}

			if i < maxRetries-1 {
				golog.Warn("Non-timeout error during body reading, retrying", "url", url, "attempt", i+1, "error", err, "errorType", errorType)
				waitTime := time.Duration((i+1)*2) * time.Second
				time.Sleep(waitTime)
				continue
			}
			return nil, fmt.Errorf("failed to read body after %d attempts: %w", maxRetries, err)
		}

		// Success! Return the data
		return data, nil
	}

	return nil, fmt.Errorf("failed to read body after %d attempts", maxRetries)
}

// downloadAndReadWithRetry downloads and reads the entire response body with retry logic
// This includes both the HTTP request and reading the response body
func downloadAndReadWithRetry(url string, maxRetries ...int) ([]byte, error) {
	retries := DEFAULT_RETRIES
	if len(maxRetries) > 0 {
		retries = maxRetries[0]
	}

	// 记录下载开始时间
	downloadStartTime := time.Now()

	// First, get the response with built-in retry logic
	resp, err := downloadWithRetry(url, retries)
	if err != nil {
		return nil, fmt.Errorf("failed to download: %w", err)
	}
	defer func() {
		if err := resp.Body.Close(); err != nil {
			golog.Error("failed to close response body", "error", err)
		}
	}()

	// Now read the response body with separate retry logic
	data, err := readBodyWithRetry(resp, url, retries)
	if err != nil {
		return nil, err
	}

	// 计算下载速度并记录日志
	downloadDuration := time.Since(downloadStartTime)
	downloadSizeBytes := int64(len(data))
	downloadSpeedBytesPerSec := float64(downloadSizeBytes) / downloadDuration.Seconds()
	downloadSpeedMBPerSec := downloadSpeedBytesPerSec / (1024 * 1024)

	golog.Info("Download completed",
		"url", url,
		"size_kb", int(downloadSizeBytes/1024),
		"size_mb", fmt.Sprintf("%.2f", float64(downloadSizeBytes)/(1024*1024)),
		"duration_ms", downloadDuration.Milliseconds(),
		"speed", fmt.Sprintf("%.2f Mb/s", downloadSpeedMBPerSec))

	return data, nil
}

// getImageExtensionFromURL extracts the image file extension from URL
// Handles complex URLs with query parameters and image processing services
func getImageExtensionFromURL(url string) string {
	// Remove query parameters first
	if idx := strings.Index(url, "?"); idx != -1 {
		url = url[:idx]
	}

	// Get the extension from the URL path
	ext := strings.ToLower(filepath.Ext(url))

	// Handle common image extensions
	switch ext {
	case ".jpg", ".jpeg":
		return ".jpg"
	case ".png":
		return ".png"
	case ".webp":
		return ".webp"
	case ".gif":
		return ".gif"
	case ".bmp":
		return ".bmp"
	case ".tiff", ".tif":
		return ".tiff"
	default:
		// If no clear extension, try to detect from URL patterns
		urlLower := strings.ToLower(url)
		if strings.Contains(urlLower, ".jpg") || strings.Contains(urlLower, ".jpeg") {
			return ".jpg"
		}
		if strings.Contains(urlLower, ".png") {
			return ".png"
		}
		if strings.Contains(urlLower, ".webp") {
			return ".webp"
		}
		// Default to unknown
		return ""
	}
}

// needsFormatConversion determines if format conversion is needed
// Only non-JPG formats need conversion (to JPG)
func needsFormatConversion(urlExt string) bool {
	// Normalize extension
	urlExt = strings.ToLower(urlExt)

	// If we can't determine URL extension, assume conversion is needed
	if urlExt == "" {
		return true
	}

	// Only JPG/JPEG don't need conversion
	return urlExt != ".jpg" && urlExt != ".jpeg"
}

// ensureDirectoryExists creates the directory for the given file path if it doesn't exist
func ensureDirectoryExists(filePath string) error {
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}
	return nil
}

// saveAsJPEG saves the image as JPEG format
// path is the full path to the file including filename and extension
func saveAsJPEG(img image.Image, path string) (string, error) {
	if img == nil {
		return "", fmt.Errorf("cannot save nil image")
	}

	// Create directory if it doesn't exist
	if err := ensureDirectoryExists(path); err != nil {
		return "", err
	}

	// Create file
	file, err := os.Create(path)
	if err != nil {
		golog.Error("failed to create output file", "path", path, "error", err)
		return "", fmt.Errorf("failed to create output file: %w", err)
	}
	defer func() {
		if err := file.Close(); err != nil {
			golog.Error("failed to close file", "path", path, "error", err)
		}
	}()

	// 记录编码和写入开始时间
	encodeStartTime := time.Now()

	if err := jpeg.Encode(file, img, &jpeg.Options{Quality: 95}); err != nil {
		golog.Error("failed to encode JPEG", "error", err)
		return "", fmt.Errorf("failed to encode JPEG: %w", err)
	}

	// 获取文件大小并计算写入速度
	fileInfo, err := file.Stat()
	if err != nil {
		golog.Warn("failed to get file size for speed calculation", "path", path, "error", err)
	} else {
		encodeDuration := time.Since(encodeStartTime)
		fileSizeBytes := fileInfo.Size()
		writeSpeedBytesPerSec := float64(fileSizeBytes) / encodeDuration.Seconds()
		writeSpeedMBPerSec := writeSpeedBytesPerSec / (1024 * 1024)

		golog.Info("JPEG encode and write completed",
			"path", path,
			"size_kb", int(fileSizeBytes/1024),
			"size_mb", fmt.Sprintf("%.2f", float64(fileSizeBytes)/(1024*1024)),
			"duration_ms", encodeDuration.Milliseconds(),
			"speed", fmt.Sprintf("%.2f Mb/s", writeSpeedMBPerSec))
	}

	return path, nil
}

// saveAsWebP saves the image as WebP format with compression
// path is the full path to the file including filename and extension
func saveAsWebP(img image.Image, path string) (string, error) {
	if img == nil {
		return "", fmt.Errorf("cannot save nil image")
	}

	// Ensure the file has .webp extension
	ext := filepath.Ext(path)
	if ext != ".webp" {
		path = path[:len(path)-len(ext)] + ".webp"
	}

	// Create directory if it doesn't exist
	if err := ensureDirectoryExists(path); err != nil {
		return "", err
	}

	file, err := os.Create(path)
	if err != nil {
		golog.Error("failed to create file", "path", path, "error", err)
		return "", fmt.Errorf("failed to create file: %w", err)
	}
	defer func() {
		if err := file.Close(); err != nil {
			golog.Error("failed to close file", "path", path, "error", err)
		}
	}()

	// 记录编码和写入开始时间
	encodeStartTime := time.Now()

	err = nativewebp.Encode(file, img, nil)
	if err != nil {
		golog.Error("failed to encode WebP", "error", err)
		return "", fmt.Errorf("failed to encode WebP: %w", err)
	}

	// 获取文件大小并计算写入速度
	fileInfo, err := file.Stat()
	if err != nil {
		golog.Warn("failed to get file size for speed calculation", "path", path, "error", err)
	} else {
		encodeDuration := time.Since(encodeStartTime)
		fileSizeBytes := fileInfo.Size()
		writeSpeedBytesPerSec := float64(fileSizeBytes) / encodeDuration.Seconds()
		writeSpeedMBPerSec := writeSpeedBytesPerSec / (1024 * 1024)

		golog.Info("WebP encode and write completed",
			"path", path,
			"size_kb", int(fileSizeBytes/1024),
			"size_mb", fmt.Sprintf("%.2f", float64(fileSizeBytes)/(1024*1024)),
			"duration_ms", encodeDuration.Milliseconds(),
			"speed", fmt.Sprintf("%.2f Mb/s", writeSpeedMBPerSec))
	}

	return path, nil
}

// SaveImage saves the provided image data to the specified path
// If compressWebP is true, attempts to convert the image to WebP format with compression
// Falls back to JPEG if WebP encoding is not available
// Returns the final saved file path and any error that occurred
func SaveImage(img image.Image, savePath string, compressWebP bool) (string, error) {
	// Create directory if it doesn't exist
	if err := ensureDirectoryExists(savePath); err != nil {
		golog.Error("failed to create directory", "error", err)
		return "", err
	}

	var finalPath string
	var err error
	if compressWebP {
		// Try to save as WebP
		finalPath, err = saveAsWebP(img, savePath)
		if err != nil {
			golog.Warn("failed to save as WebP, falling back to JPEG", "error", err)
			// Change extension to .jpg for fallback to avoid file extension mismatch
			ext := filepath.Ext(savePath)
			jpgPath := savePath[:len(savePath)-len(ext)] + ".jpg"
			return saveAsJPEG(img, jpgPath)
		}
	} else {
		// Save as JPEG
		finalPath, err = saveAsJPEG(img, savePath)
		if err != nil {
			return "", err
		}
	}

	golog.Info("Successfully saved image",
		"path", finalPath,
		"compressed", compressWebP)

	return finalPath, nil
}

// downloadImageData downloads image data with memory management and retry logic
// Returns the raw data without validation for better performance
func downloadImageData(url string, maxRetries int) ([]byte, error) {
	// Download file and read data with retry (including body reading)
	data, err := downloadAndReadWithRetry(url, maxRetries)
	if err != nil {
		return nil, fmt.Errorf("failed to download file: %w", err)
	}

	// Check file size for warnings and optimizations
	sizeMB := float64(len(data)) / (1024 * 1024)

	// Memory preparation for very large files (>10MB)
	if len(data) > MAX_IMAGE_SIZE_BYTES { // Only for files > 10MB (doubled threshold)
		golog.Warn("Large image detected",
			"sizeMB", sizeMB,
			"url", url)
		// For very large files, use smart GC instead of immediate GC
		smartForceGC("very large image detected (>10MB)")
		time.Sleep(500 * time.Millisecond) // Much shorter wait
	}

	// Log image data size for monitoring
	dataSizeMB := float64(len(data)) / (1024 * 1024)
	golog.Debug("Processing image", "sizeMB", dataSizeMB, "url", url)

	if dataSizeMB > MAX_IMAGE_MEMORY_MB {
		golog.Warn("Large image detected, may cause memory issues",
			"sizeMB", dataSizeMB, "maxMB", MAX_IMAGE_MEMORY_MB, "url", url)
	}

	return data, nil
}

// DownloadAndSaveImageInDirs downloads an image from URL and saves it to specified paths
// savePaths is a list of full paths including filename and extension
// If compressWebP is true, attempts to convert the image to WebP format with compression
// Falls back to JPEG if WebP encoding is not available
// When compressWebP=false, converts to JPEG only if target extension is .jpg/.jpeg, otherwise saves original data
// Returns a map [savePath]savedPath and any error that occurred
func DownloadAndSaveImageInDirs(url string, savePaths []string, compressWebP bool, maxRetries ...int) (map[string]string, error) {
	return DownloadAndSaveImageInDirsWithCompression(url, savePaths, compressWebP, false, 0, maxRetries...)
}

// DownloadAndSaveImageInDirsWithCompression downloads an image from URL and saves it to specified paths with optional smart compression
// savePaths is a list of full paths including filename and extension
// If compressWebP is true, attempts to convert the image to WebP format with compression
// If compressToSize is true, intelligently compresses the image to targetSizeKB
// Returns a map [savePath]savedPath and any error that occurred
func DownloadAndSaveImageInDirsWithCompression(url string, savePaths []string, compressWebP bool, compressToSize bool, targetSizeKB int, maxRetries ...int) (map[string]string, error) {
	retries := DEFAULT_RETRIES
	if len(maxRetries) > 0 {
		retries = maxRetries[0]
	}

	// Download image data without validation for better performance
	rawData, err := downloadImageData(url, retries)
	if err != nil {
		return nil, err
	}

	// Early optimization: check if we can save raw data directly without decoding
	urlExt := getImageExtensionFromURL(url)
	needsConversion := needsFormatConversion(urlExt)
	rawDataSizeKB := len(rawData) / 1024

	// Set default target size if needed
	if targetSizeKB <= 0 {
		targetSizeKB = DEFAULT_TARGET_SIZE_KB
	}

	results := make(map[string]string)
	for _, savePath := range savePaths {
		var savedPath string
		var saveErr error

		// Fast path: if no WebP compression, size is within target, and no format conversion needed
		// then save raw data directly without decoding/encoding
		if (!compressWebP) && (rawDataSizeKB <= targetSizeKB) && (!needsConversion) {
			golog.Info("Fast path: saving raw data directly",
				"path", savePath,
				"size_kb", rawDataSizeKB,
				"target_size_kb", targetSizeKB,
				"url_ext", urlExt)
			savedPath, saveErr = saveFileFromData(rawData, savePath)
			if saveErr != nil {
				golog.Error("failed to save raw image data", "path", savePath, "error", saveErr)
				continue
			}
			results[savePath] = savedPath
			continue
		}

		// Standard path: decode image for processing
		img, _, decodeErr := image.Decode(bytes.NewReader(rawData))
		if decodeErr != nil {
			golog.Error("failed to decode image", "path", savePath, "error", decodeErr)
			continue
		}

		// If smart compression is enabled
		if compressToSize && targetSizeKB > 0 {
			// Resize image to max dimension
			compressedImg, finalSizeKB, compressErr := compressImageToTargetSize(img, targetSizeKB, compressWebP)
			if compressErr != nil {
				golog.Error("failed to compress image to target size", "path", savePath, "error", compressErr)
				// If compression fails, continue with original image
			} else {
				img = compressedImg
				golog.Info("Image compressed successfully",
					"path", savePath,
					"target_size_kb", targetSizeKB,
					"final_size_kb", finalSizeKB)
			}
		}

		// Save processed image
		// Since we're here, either compressWebP=true, or needsConversion=true, or compressToSize was applied
		// In all cases, we need to save the processed image (not raw data)
		savedPath, saveErr = processAndSaveImageWithCompression(img, savePath, compressWebP)

		if saveErr != nil {
			golog.Error("failed to save image", "path", savePath, "error", saveErr)
			continue
		}
		results[savePath] = savedPath
	}

	if len(results) == 0 {
		return nil, fmt.Errorf("failed to save image to any directory")
	}

	return results, nil
}

// DownloadAndResizeImage downloads an image from URL and returns the resized image
// width and height are the target dimensions, the image will maintain its aspect ratio
// and fit within these dimensions
func DownloadAndResizeImage(url string, width, height int) (image.Image, error) {
	// Validate dimensions
	if width <= 0 || height <= 0 {
		return nil, fmt.Errorf("invalid dimensions: width and height must be positive")
	}

	// Use global HTTP client to prevent connection leaks
	client := getHTTPClient()
	resp, err := client.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to download image: %w", err)
	}
	defer func() {
		if err := resp.Body.Close(); err != nil {
			golog.Error("failed to close response body", "error", err)
		}
	}()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status %d while downloading image", resp.StatusCode)
	}
	// Decode image
	img, _, err := image.Decode(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Resize image
	bounds := img.Bounds()
	imgWidth := bounds.Dx()
	imgHeight := bounds.Dy()

	// Calculate new dimensions maintaining aspect ratio
	var newWidth, newHeight int
	if imgWidth > imgHeight {
		newWidth = width
		newHeight = int(float64(imgHeight) * float64(width) / float64(imgWidth))
	} else {
		newHeight = height
		newWidth = int(float64(imgWidth) * float64(height) / float64(imgHeight))
	}
	// Avoid height or width is 0
	if newHeight == 0 {
		newHeight = 1
	}
	if newWidth == 0 {
		newWidth = 1
	}

	// Create new image
	newImg := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))

	// Scale image using high-quality CatmullRom interpolation
	xdraw.CatmullRom.Scale(newImg, newImg.Bounds(), img, img.Bounds(), draw.Over, nil)

	return newImg, nil
}

// processAndSaveImage processes and saves an image with the specified compression
// This is a wrapper around SaveImage without directory creation and logging
func processAndSaveImage(img image.Image, savePath string, compressWebP bool) (string, error) {
	if compressWebP {
		savedPath, err := saveAsWebP(img, savePath)
		if err != nil {
			golog.Warn("failed to save as WebP, falling back to JPEG", "error", err)
			// Change extension to .jpg for fallback
			ext := filepath.Ext(savePath)
			jpgPath := savePath[:len(savePath)-len(ext)] + ".jpg"
			return saveAsJPEG(img, jpgPath)
		}
		return savedPath, nil
	} else {
		return saveAsJPEG(img, savePath)
	}
}

// processAndSaveImageWithCompression processes and saves an image with the specified compression
// This version is used when the image has already been processed/compressed
func processAndSaveImageWithCompression(img image.Image, savePath string, compressWebP bool) (string, error) {
	if compressWebP {
		savedPath, err := saveAsWebP(img, savePath)
		if err != nil {
			golog.Warn("failed to save as WebP, falling back to JPEG", "error", err)
			// Change extension to .jpg for fallback
			ext := filepath.Ext(savePath)
			jpgPath := savePath[:len(savePath)-len(ext)] + ".jpg"
			return saveAsJPEG(img, jpgPath)
		}
		return savedPath, nil
	} else {
		return saveAsJPEG(img, savePath)
	}
}

// saveFileFromData saves data from a byte slice to a file path
// This is used when we already have the data in memory
func saveFileFromData(data []byte, filePath string) (string, error) {
	// Ensure directory exists
	if err := ensureDirectoryExists(filePath); err != nil {
		return "", err
	}

	// 记录写入开始时间
	writeStartTime := time.Now()

	// Write data to file
	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return "", fmt.Errorf("failed to write file %s: %w", filePath, err)
	}

	// 计算写入速度并记录日志
	writeDuration := time.Since(writeStartTime)
	writeSizeBytes := int64(len(data))
	writeSpeedBytesPerSec := float64(writeSizeBytes) / writeDuration.Seconds()
	writeSpeedMBPerSec := writeSpeedBytesPerSec / (1024 * 1024)

	golog.Info("File write completed",
		"path", filePath,
		"size_kb", int(writeSizeBytes/1024),
		"size_mb", fmt.Sprintf("%.2f", float64(writeSizeBytes)/(1024*1024)),
		"duration_ms", writeDuration.Milliseconds(),
		"speed", fmt.Sprintf("%.2f Mb/s", writeSpeedMBPerSec))

	return filePath, nil
}

// saveFileStreaming saves file content using streaming to avoid loading entire file into memory
func saveFileStreaming(reader io.Reader, savePath string) (string, error) {
	// Create directory if it doesn't exist
	if err := ensureDirectoryExists(savePath); err != nil {
		return "", err
	}

	// Create the file
	file, err := os.Create(savePath)
	if err != nil {
		return "", fmt.Errorf("failed to create file: %w", err)
	}
	defer func() {
		if err := file.Close(); err != nil {
			golog.Error("failed to close file", "error", err)
		}
	}()

	// 记录写入开始时间
	writeStartTime := time.Now()

	// Copy data from reader to file using streaming
	bytesWritten, err := io.Copy(file, reader)
	if err != nil {
		return "", fmt.Errorf("failed to copy data to file: %w", err)
	}

	// 计算写入速度并记录日志
	writeDuration := time.Since(writeStartTime)
	writeSpeedBytesPerSec := float64(bytesWritten) / writeDuration.Seconds()
	writeSpeedMBPerSec := writeSpeedBytesPerSec / (1024 * 1024)

	golog.Info("Streaming write completed",
		"path", savePath,
		"size_kb", int(bytesWritten/1024),
		"size_mb", fmt.Sprintf("%.2f", float64(bytesWritten)/(1024*1024)),
		"duration_ms", writeDuration.Milliseconds(),
		"speed", fmt.Sprintf("%.2f Mb/s", writeSpeedMBPerSec))

	return savePath, nil
}

// saveNotPic saves a file in its original format
// Returns the path to the saved file and any error that occurred
func saveNotPic(data []byte, savePath string) (string, error) {
	// Create directory if it doesn't exist
	if err := ensureDirectoryExists(savePath); err != nil {
		return "", err
	}

	// Create output file
	file, err := os.Create(savePath)
	if err != nil {
		return "", fmt.Errorf("failed to create output file: %w", err)
	}
	defer func() {
		if err := file.Close(); err != nil {
			golog.Error("failed to close file", "path", savePath, "error", err)
		}
	}()

	// Write data to file
	if _, err := file.Write(data); err != nil {
		return "", fmt.Errorf("failed to save file: %w", err)
	}

	golog.Info("Successfully saved file", "path", savePath)
	return savePath, nil
}

// checkMemoryAvailable checks if there's sufficient memory available for image processing
func checkMemoryAvailable() bool {
	// Read memory stats
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// Check if current memory usage is too high - optimized for worker pool architecture
	numGoroutines := runtime.NumGoroutine()

	// With worker pool, normal goroutine count should be ~60, so configurable threshold allows adaptation
	if m.Alloc > uint64(maxMemoryForProcessing) || numGoroutines > maxGoroutines {
		golog.Debug("Memory or goroutine usage high",
			"currentAlloc", formatMemoryBytes(m.Alloc),
			"limit", formatMemoryBytes(uint64(maxMemoryForProcessing)),
			"numGoroutines", numGoroutines,
			"maxGoroutines", maxGoroutines)
		return false
	}

	golog.Debug("Memory check passed", "currentAlloc", formatMemoryBytes(m.Alloc), "numGoroutines", numGoroutines)
	return true
}

// smartForceGC implements intelligent GC with much lower frequency
// Only triggers GC when really necessary (1000 images processed, high memory, or time interval)
func smartForceGC(message ...string) {
	count := atomic.AddInt64(&processedImageCount, 1)
	now := time.Now()

	shouldGC := false
	gcReason := ""

	// 1. Processed a certain number of images
	if count%gcBatchSize == 0 {
		shouldGC = true
		gcReason = fmt.Sprintf("batch processing (%d images)", count)
	}

	// 2. Memory usage is high and the last GC was more than the minimum interval ago
	if !shouldGC {
		var m runtime.MemStats
		runtime.ReadMemStats(&m)
		emergencyThreshold := uint64(gcEmergencyMemoryGB * 1024 * 1024 * 1024)

		if m.Alloc > emergencyThreshold && now.Sub(lastGCTime) > gcMinInterval {
			shouldGC = true
			gcReason = fmt.Sprintf("emergency memory pressure (%.1fGB)", float64(m.Alloc)/(1024*1024*1024))
		}
	}

	if shouldGC {
		runtime.GC()
		lastGCTime = now

		var m runtime.MemStats
		runtime.ReadMemStats(&m)

		reason := gcReason
		if len(message) > 0 && message[0] != "" {
			reason = fmt.Sprintf("%s - %s", gcReason, message[0])
		}

		golog.Info("Smart GC executed",
			"reason", reason,
			"processedImages", count,
			"memoryMB", m.Alloc/(1024*1024),
			"goroutines", runtime.NumGoroutine())
	}
}

// forceGC forces garbage collection to free up memory (legacy function for compatibility)
// Optional message parameter for logging context
func forceGC(message ...string) {
	// For backward compatibility, redirect to smartForceGC
	smartForceGC(message...)
}

// formatMemoryBytes formats bytes into human readable format
func formatMemoryBytes(bytes uint64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// compressImageToTargetSize resizes image by setting the larger dimension to MAX_DIMENSION_PIXELS
// while maintaining aspect ratio. This function assumes the caller has already determined
// that compression/resizing is needed.
func compressImageToTargetSize(img image.Image, targetSizeKB int, isWebP bool) (image.Image, int, error) {
	if img == nil {
		return nil, 0, fmt.Errorf("cannot compress nil image")
	}

	if targetSizeKB <= 0 {
		targetSizeKB = DEFAULT_TARGET_SIZE_KB // Default target size
	}

	bounds := img.Bounds()
	originalWidth := bounds.Dx()
	originalHeight := bounds.Dy()

	golog.Info("Starting image resize to max dimension",
		"target_size_kb", targetSizeKB,
		"original_width", originalWidth,
		"original_height", originalHeight,
		"max_dimension", MAX_DIMENSION_PIXELS)

	// Set the larger dimension to MAX_DIMENSION_PIXELS while maintaining aspect ratio
	maxDimension := MAX_DIMENSION_PIXELS
	var newWidth, newHeight int

	if originalWidth > originalHeight {
		// Width is the larger dimension
		if originalWidth > maxDimension {
			newWidth = maxDimension
			newHeight = int(float64(originalHeight) * float64(maxDimension) / float64(originalWidth))
		} else {
			newWidth = originalWidth
			newHeight = originalHeight
		}
	} else {
		// Height is the larger dimension
		if originalHeight > maxDimension {
			newHeight = maxDimension
			newWidth = int(float64(originalWidth) * float64(maxDimension) / float64(originalHeight))
		} else {
			newWidth = originalWidth
			newHeight = originalHeight
		}
	}

	// Ensure dimensions are not zero
	if newWidth < 1 {
		newWidth = 1
	}
	if newHeight < 1 {
		newHeight = 1
	}

	// If dimensions haven't changed, return original image
	if newWidth == originalWidth && newHeight == originalHeight {
		golog.Info("No resize needed, returning original image",
			"width", originalWidth,
			"height", originalHeight)

		// Since no resize was needed, encode to get the actual final size
		var testData []byte
		var err error
		if isWebP {
			testData, err = encodeImageToWebP(img)
		} else {
			testData, err = encodeImageToJPEG(img, 95)
		}

		if err != nil {
			return nil, 0, fmt.Errorf("failed to encode image for final size: %w", err)
		}

		finalSizeKB := len(testData) / 1024
		return img, finalSizeKB, nil
	}

	// Create resized image
	resizedImg := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))
	xdraw.CatmullRom.Scale(resizedImg, resizedImg.Bounds(), img, img.Bounds(), draw.Over, nil)

	// Test the file size after resizing
	var testData []byte
	var err error
	if isWebP {
		testData, err = encodeImageToWebP(resizedImg)
	} else {
		testData, err = encodeImageToJPEG(resizedImg, 95)
	}

	if err != nil {
		return nil, 0, fmt.Errorf("failed to encode resized image for size test: %w", err)
	}

	finalSizeKB := len(testData) / 1024

	golog.Info("Smart compression completed",
		"final_size_kb", finalSizeKB,
		"original_width", originalWidth,
		"original_height", originalHeight,
		"final_width", newWidth,
		"final_height", newHeight,
		"max_dimension", maxDimension)

	return resizedImg, finalSizeKB, nil
}

// encodeImageToJPEG 将图片编码为JPEG格式的字节数据
func encodeImageToJPEG(img image.Image, quality int) ([]byte, error) {
	var buf bytes.Buffer
	err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: quality})
	if err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

// encodeImageToWebP 将图片编码为WebP格式的字节数据
func encodeImageToWebP(img image.Image) ([]byte, error) {
	var buf bytes.Buffer
	err := nativewebp.Encode(&buf, img, nil)
	if err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

type DownloadAndSaveFileOptions struct {
	URL            string   // URL to download from
	SavePaths      []string // Paths to save the file to
	CompressWebP   bool     // Whether to compress as WebP (only for images)
	IsPhoto        bool     // Whether the file is a photo
	MaxRetries     int      // Maximum number of retry attempts
	CompressToSize bool     // Whether to enable smart compression to target size
	TargetSizeKB   int      // Target file size in KB (default: 300)
}

// DownloadAndSaveFile downloads a file and saves it to the specified format
// Returns the path to the saved file and any error that occurred
func DownloadAndSaveFile(opts *DownloadAndSaveFileOptions) ([]string, error) {
	if opts == nil {
		return nil, fmt.Errorf("options are nil")
	}
	if opts.MaxRetries == 0 {
		opts.MaxRetries = DEFAULT_RETRIES
	}

	if len(opts.SavePaths) == 0 {
		return nil, fmt.Errorf("no save paths provided")
	}

	results := make([]string, 0)

	// For photos, delegate to the specialized image processing function with memory management
	// Note: DownloadAndSaveImageInDirs now uses direct writing (no decode/encode) when compressWebP=false
	if opts.IsPhoto {
		var resultMap map[string]string
		var err error

		// Set default target size
		targetSizeKB := opts.TargetSizeKB
		if targetSizeKB <= 0 {
			targetSizeKB = DEFAULT_TARGET_SIZE_KB // Default target size
		}

		// 使用智能压缩版本的函数
		if opts.CompressToSize {
			resultMap, err = DownloadAndSaveImageInDirsWithCompression(opts.URL, opts.SavePaths, opts.CompressWebP, true, targetSizeKB, opts.MaxRetries)
		} else {
			resultMap, err = DownloadAndSaveImageInDirs(opts.URL, opts.SavePaths, opts.CompressWebP, opts.MaxRetries)
		}

		if err != nil {
			return nil, err
		}

		// Convert map[string]string to []string for compatibility
		results := make([]string, 0, len(resultMap))
		for _, savedPath := range resultMap {
			results = append(results, savedPath)
		}

		// Smart GC - only when really necessary (1000 images or high memory pressure)
		smartForceGC("image processing completed")
		return results, nil
	}

	// For non-photo files, download and save directly using response stream
	resp, err := downloadWithRetry(opts.URL, opts.MaxRetries)
	if err != nil {
		return nil, fmt.Errorf("failed to download file: %w", err)
	}
	defer func() {
		if err := resp.Body.Close(); err != nil {
			golog.Error("failed to close response body", "error", err)
		}
	}()

	if len(opts.SavePaths) == 1 {
		// Single path: save directly from response stream (most efficient)
		savedPath, saveErr := saveFileStreaming(resp.Body, opts.SavePaths[0])
		if saveErr != nil {
			golog.Error("failed to save file", "path", opts.SavePaths[0], "error", saveErr)
			return nil, fmt.Errorf("failed to save file to %s: %w", opts.SavePaths[0], saveErr)
		}
		results = append(results, savedPath)
	} else {
		// Multiple paths: need to read data once and save to all paths
		golog.Debug("Multiple save paths detected, saving to all paths", "paths", len(opts.SavePaths))

		// Read data once for multiple saves
		data, readErr := readBodyWithRetry(resp, opts.URL, opts.MaxRetries)
		if readErr != nil {
			return nil, fmt.Errorf("failed to read file data: %w", readErr)
		}

		for _, savePath := range opts.SavePaths {
			savedPath, saveErr := saveFileFromData(data, savePath)
			if saveErr != nil {
				golog.Error("failed to save file", "path", savePath, "error", saveErr)
				return nil, fmt.Errorf("failed to save file to %s: %w", savePath, saveErr)
			}
			results = append(results, savedPath)
		}
	}

	if len(results) == 0 {
		return nil, fmt.Errorf("failed to save file to any directory")
	}

	golog.Debug("Successfully downloaded and saved file", "paths", len(results))

	return results, nil
}

// DownloadAndSaveImageWithSmartCompression downloads image and intelligently compresses to specified size
// This is a convenience function for quick implementation of image compression functionality
func DownloadAndSaveImageWithSmartCompression(url string, savePath string, targetSizeKB int, compressWebP bool, maxRetries ...int) (string, error) {
	if targetSizeKB <= 0 {
		targetSizeKB = DEFAULT_TARGET_SIZE_KB // Default target size
	}

	resultMap, err := DownloadAndSaveImageInDirsWithCompression(url, []string{savePath}, compressWebP, true, targetSizeKB, maxRetries...)
	if err != nil {
		return "", err
	}

	if savedPath, exists := resultMap[savePath]; exists {
		return savedPath, nil
	}

	return "", fmt.Errorf("failed to save image to %s", savePath)
}
